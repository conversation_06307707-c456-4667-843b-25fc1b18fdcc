{"name": "justcooked", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "test": "vitest run --config vitest.config.ts", "test:watch": "vitest --config vitest.config.ts", "test:coverage": "vitest run --coverage --config vitest.config.ts", "test:ci": "vitest run --coverage --config vitest.config.ts", "test:rust": "cd src-tauri && cargo test", "test:rust:unit": "cd src-tauri && cargo test --lib", "test:rust:integration": "cd src-tauri && cargo test --test integration_tests", "test:rust:property": "cd src-tauri && cargo test --test property_tests", "test:rust:coverage": "cd src-tauri && cargo tarpaulin --out Html --output-dir coverage/", "test:all": "npm run test && npm run test:rust", "test:all:coverage": "npm run test:coverage && npm run test:rust:coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/system": "^7.1.0", "@mui/x-date-pickers": "^8.5.2", "@reduxjs/toolkit": "^2.8.2", "@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-dialog": "^2.2.2", "@tauri-apps/plugin-fs": "^2.3.0", "@tauri-apps/plugin-http": "^2.4.4", "@tauri-apps/plugin-shell": "^2.2.1", "@zxing/browser": "^0.1.5", "date-fns": "^4.1.0", "react": "19.1.0", "react-dom": "19.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.1"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.0", "@storybook/addon-a11y": "^9.0.12", "@storybook/addon-actions": "^9.0.8", "@storybook/addon-docs": "^9.0.12", "@storybook/addon-vitest": "^9.0.12", "@storybook/react-vite": "^9.0.12", "@tauri-apps/cli": "2.5.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "19.1.6", "@types/react-dom": "19.1.5", "@types/react-redux": "^7.1.34", "@vitejs/plugin-react": "^4.3.4", "@vitest/browser": "3.2.3", "@vitest/coverage-v8": "3.2.3", "@vitest/expect": "^3.2.3", "@vitest/ui": "^3.2.3", "jsdom": "^26.1.0", "playwright": "^1.53.1", "storybook": "^9.0.12", "typescript": "~5.6.2", "vite": "^6.3.5", "vitest": "^3.2.3"}}