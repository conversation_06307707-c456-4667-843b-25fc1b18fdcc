// Core types
export type { Recipe } from './recipeType';
export type { Ingredient } from './basicIngredient';
export type { NutritionalInfo } from './NutritionalInfo';

// Ingredient-related types
export type { IngredientDatabase } from './ingredientDatabase';
export type { IngredientCategory } from './ingredientCategory';
export { INGREDIENT_CATEGORIES } from './ingredientCategory';
export type { IngredientSearchResult } from './ingredientSearchResult';
export type { StorageInfo } from './storageInfo';

// Recipe-related types
export type { PantryItem } from './pantryItem';
export type { RecipeCollection } from './recipeCollection';

// Product types
export type { Product, ProductSearchResult } from './product';
export type { ProductIngredientMapping, CreateProductIngredientMappingRequest, IngredientAssociation } from './productIngredientMapping';

// Meal planning types
export type { MealPlan, MealPlanRecipe, MealPlanSettings, MealType } from './mealPlan';
export { MEAL_TYPES, DEFAULT_MEAL_PLAN_SETTINGS, getMealTypeDisplayName, getMealTypeOrder } from './mealPlan';

// Meal planning utility functions (re-exported from services)
export {
  isMealPlanActive,
  isMealPlanUpcoming,
  isMealPlanPast,
  getMealPlanDuration,
  getMealPlanDates
} from '../services/mealPlanStorage';
export type { ShoppingList, ShoppingListItem, ShoppingCategory } from './shoppingList';
export { SHOPPING_CATEGORIES, getCategoryDisplayName, getCategoryOrder, categorizeIngredient } from './shoppingList';

// Batch import types
export type {
  BatchImportRequest,
  BatchImportProgress,
  BatchImportResult,
  BatchImportError,
  CategoryInfo,
  RecipeUrlInfo,
  BatchImportConfig
} from './batchImport';
export { BatchImportStatus, DEFAULT_BATCH_IMPORT_CONFIG } from './batchImport';

// Re-import types
export type {
  ReImportRequest,
  ReImportProgress,
  ReImportResult,
  ReImportOptions
} from './reImport';

// Import queue types
export type {
  ImportQueueTask,
  ImportQueueStatus,
  QueueProgressUpdate,
  ImportQueueConfig
} from './importQueue';
export { ImportQueueTaskStatus, DEFAULT_IMPORT_QUEUE_CONFIG } from './importQueue';

// Search-related types
export type { SearchFilters } from './searchFilters';
export type { RecentSearch } from './recentSearch';

// Raw ingredient types for parsing analysis
export type { RawIngredient } from './rawIngredient';

// Database management types
export type {
  DatabaseExport,
  DatabaseImportResult,
  DatabaseStats,
  DatabaseOperationProgress
} from './databaseManagement';
export { DatabaseOperation } from './databaseManagement';
