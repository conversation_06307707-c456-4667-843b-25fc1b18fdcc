import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { fn } from 'storybook/test';
import { userEvent, within, expect } from 'storybook/test';
import { vi } from 'vitest';
import BarcodeScanner from './BarcodeScanner';

// Mock the logging service
vi.mock('@services/loggingService', () => ({
  createLogger: vi.fn(() => ({
    info: vi.fn().mockResolvedValue(undefined),
    debug: vi.fn().mockResolvedValue(undefined),
    warn: vi.fn().mockResolvedValue(undefined),
    error: vi.fn().mockResolvedValue(undefined),
    logError: vi.fn().mockResolvedValue(undefined),
    logUserAction: vi.fn().mockResolvedValue(undefined),
  })),
}));

// Mock ZXing browser library
const mockReset = vi.fn();
const mockDecodeFromVideoDevice = vi.fn();
const mockDecodeFromVideoElement = vi.fn();
const mockListVideoInputDevices = vi.fn().mockResolvedValue([
  { deviceId: 'camera1', label: 'Camera 1', kind: 'videoinput' }
]);

vi.mock('@zxing/browser', () => {
  const BrowserMultiFormatReaderMock: any = vi.fn().mockImplementation(() => ({
    decodeFromVideoDevice: mockDecodeFromVideoDevice,
    decodeFromVideoElement: mockDecodeFromVideoElement,
    reset: mockReset,
    hints: new Map(),
  }));

  // Add static method
  BrowserMultiFormatReaderMock.listVideoInputDevices = mockListVideoInputDevices;

  return {
    BrowserMultiFormatReader: BrowserMultiFormatReaderMock,
  };
});

// Mock ZXing library
vi.mock('@zxing/library', () => ({
  NotFoundException: class NotFoundException extends Error {
    constructor(message?: string) {
      super(message);
      this.name = 'NotFoundException';
    }
  },
  DecodeHintType: {
    TRY_HARDER: 'TRY_HARDER',
    POSSIBLE_FORMATS: 'POSSIBLE_FORMATS',
    ASSUME_GS1: 'ASSUME_GS1',
  },
  BarcodeFormat: {
    UPC_A: 'UPC_A',
    UPC_E: 'UPC_E',
    EAN_13: 'EAN_13',
    EAN_8: 'EAN_8',
    CODE_128: 'CODE_128',
    CODE_39: 'CODE_39',
    ITF: 'ITF',
    RSS_14: 'RSS_14',
    RSS_EXPANDED: 'RSS_EXPANDED',
  },
}));

// Mock navigator.mediaDevices
const mockGetUserMedia = vi.fn();
const mockStream = {
  getTracks: vi.fn(() => [
    { stop: vi.fn() }
  ])
};

Object.defineProperty(global.navigator, 'mediaDevices', {
  writable: true,
  value: {
    getUserMedia: mockGetUserMedia,
  },
});

const meta: Meta<typeof BarcodeScanner> = {
  title: 'Input/BarcodeScanner',
  component: BarcodeScanner,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    onClose: { action: 'closed' },
    onScan: { action: 'scanned' },
    open: { control: 'boolean' },
  },
  args: {
    onClose: fn(),
    onScan: fn(),
    open: true,
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// DefaultOpen: Initial state when opened
export const DefaultOpen: Story = {
  beforeEach: () => {
    vi.clearAllMocks();
    mockGetUserMedia.mockResolvedValue(mockStream);
    mockDecodeFromVideoElement.mockImplementation(() => new Promise(() => {})); // Never resolves
  },
};

// RequestingPermission: Simulate delay in permission
export const RequestingPermission: Story = {
  beforeEach: () => {
    vi.clearAllMocks();
    // Create a promise that takes a long time to resolve
    mockGetUserMedia.mockImplementation(() => 
      new Promise(resolve => setTimeout(() => resolve(mockStream), 5000))
    );
    mockDecodeFromVideoElement.mockImplementation(() => new Promise(() => {}));
  },
};

// PermissionDenied: getUserMedia rejects with NotAllowedError
export const PermissionDenied: Story = {
  beforeEach: () => {
    vi.clearAllMocks();
    const permissionError = new Error('Permission denied');
    permissionError.name = 'NotAllowedError';
    mockGetUserMedia.mockRejectedValue(permissionError);
  },
};

// NoCameraFound: listVideoInputDevices returns empty
export const NoCameraFound: Story = {
  beforeEach: () => {
    vi.clearAllMocks();
    mockGetUserMedia.mockResolvedValue(mockStream);
    mockListVideoInputDevices.mockResolvedValue([]); // No cameras
    mockDecodeFromVideoElement.mockImplementation(() => new Promise(() => {}));
  },
};

// Scanning: Simulate active scanning state
export const Scanning: Story = {
  beforeEach: () => {
    vi.clearAllMocks();
    mockGetUserMedia.mockResolvedValue(mockStream);
    mockDecodeFromVideoElement.mockImplementation(() => new Promise(() => {}));
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    
    // Wait for the video element to appear
    await canvas.findByTestId('barcodeScanner-video-main');
    
    // The component should be in scanning state
    expect(canvas.queryByTestId('barcodeScanner-indicator-loading')).not.toBeInTheDocument();
  },
};

// ManualInput: Focus on manual input section
export const ManualInput: Story = {
  beforeEach: () => {
    vi.clearAllMocks();
    // Simulate no camera available to force manual input
    const noCameraError = new Error('No camera devices found');
    mockGetUserMedia.mockRejectedValue(noCameraError);
    mockListVideoInputDevices.mockResolvedValue([]);
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    
    // Wait for error to appear and manual input to be available
    await canvas.findByText(/No camera devices found/);
    
    // Find manual input field
    const manualInput = canvas.getByTestId('barcodeScanner-input-manual');
    expect(manualInput).toBeInTheDocument();
    
    // Type a barcode
    await userEvent.type(manualInput, '123456789012');
    
    // Click submit button
    const submitButton = canvas.getByTestId('barcodeScanner-button-submitManual');
    await userEvent.click(submitButton);
    
    // Verify onScan was called
    await expect(args.onScan).toHaveBeenCalledWith('123456789012');
  },
};

// Test Barcode Interaction
export const TestBarcodeInteraction: Story = {
  beforeEach: () => {
    vi.clearAllMocks();
    mockGetUserMedia.mockResolvedValue(mockStream);
    mockDecodeFromVideoElement.mockImplementation(() => new Promise(() => {}));
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    
    // Wait for the test barcode button to appear
    const testButton = await canvas.findByTestId('barcodeScanner-button-testBarcode');
    expect(testButton).toHaveTextContent('Use Test Food Product (041196912586)');
    
    // Click the test barcode button
    await userEvent.click(testButton);
    
    // Verify onScan was called with the test barcode
    await expect(args.onScan).toHaveBeenCalledWith('041196912586');
    await expect(args.onClose).toHaveBeenCalled();
  },
};

// Close Dialog Interaction
export const CloseDialogInteraction: Story = {
  beforeEach: () => {
    vi.clearAllMocks();
    mockGetUserMedia.mockResolvedValue(mockStream);
    mockDecodeFromVideoElement.mockImplementation(() => new Promise(() => {}));
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    
    // Wait for the close button to appear
    const closeButton = await canvas.findByTestId('barcodeScanner-button-close');
    
    // Click the close button
    await userEvent.click(closeButton);
    
    // Verify onClose was called
    await expect(args.onClose).toHaveBeenCalled();
  },
};
